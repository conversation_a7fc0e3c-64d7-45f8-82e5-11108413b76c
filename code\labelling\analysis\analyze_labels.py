import csv
import re
from collections import Counter, defaultdict
from pathlib import Path
from typing import Dict, Set, <PERSON><PERSON>

import matplotlib.colors as mcolors
import matplotlib.pyplot as plt
import numpy as np

# Get project root and set up paths
PROJECT_ROOT = Path(__file__).parent.parent.parent.parent
LABELS_DIR = PROJECT_ROOT / 'data' / 'labels'

# Filter arrays - modify these to filter the analysis
# Set to empty list to include all dates/names
# #######
# # ORIGINAL BATCH
# FILTER_DATES = ['2025-06-16']
# FILTER_NAMES = ['peter', 'ilse', 'michelle', 'rick']
# OUTPUT_DIR = Path(__file__).parent / 'data_2025-06-16'
# #######

# #######
# # SECOND BATCH
# FILTER_DATES = ['2025-08-11', '2025-08-12']
# FILTER_NAMES = ['peter', 'ilse', 'hannes', 'michelle', 'rick']
# OUTPUT_DIR = Path(__file__).parent / 'data_2025-08-11_5pers'
# #######

#######
# SECOND BATCH (excl hannes)
FILTER_DATES = ['2025-08-11', '2025-08-12']
FILTER_NAMES = ['peter', 'ilse', 'michelle', 'rick']
OUTPUT_DIR = Path(__file__).parent / 'data_2025-08-11_4pers'
#######

LOCATIONS = ['mutshoek', 'vosdeel']


def ensure_output_dir():
    """Create output directory if it doesn't exist."""
    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)


def load_labels_data():
    """
    Load all label data from CSV files.
    Returns: {location: {date: {person: [(image_name, label, image_index), ...]}}}
    """
    data = defaultdict(lambda: defaultdict(lambda: defaultdict(list)))

    for location in LOCATIONS:
        location_dir = LABELS_DIR / location
        if not location_dir.exists():
            continue

        for date_dir in location_dir.iterdir():
            if not date_dir.is_dir():
                continue

            date = date_dir.name

            # Apply date filter
            if FILTER_DATES and date not in FILTER_DATES:
                continue

            for csv_file in date_dir.glob('*.csv'):
                person = csv_file.stem

                # Apply name filter
                if FILTER_NAMES and person not in FILTER_NAMES:
                    continue

                try:
                    with open(csv_file, 'r', encoding='utf-8') as f:
                        reader = csv.DictReader(f)
                        for row in reader:
                            image_name = row.get('image_name', '')
                            label = row.get('label', '')
                            image_index = row.get('image_index', '')

                            # Handle None values
                            if image_name is None:
                                image_name = ''
                            if label is None:
                                label = ''
                            if image_index is None:
                                image_index = ''

                            image_name = image_name.strip()
                            label = label.strip()
                            image_index = image_index.strip()

                            # Ignore lines with no actual label
                            if not label or label.lower() in ['', 'none', 'null']:
                                continue

                            # Convert image_index to int, default to 0 if invalid
                            try:
                                image_index_int = int(image_index) if image_index else 0
                            except ValueError:
                                image_index_int = 0

                            data[location][date][person].append((image_name, label, image_index_int))

                except Exception as e:
                    print(f'Error reading {csv_file}: {e}')

    return data


def get_all_images_labeled_by_all_people(data: Dict) -> Set[str]:
    """
    Find all images that have been labeled by ALL people in the filter.
    Returns set of image names.
    """
    if not FILTER_NAMES:
        return set()

    # Collect all images per person
    images_per_person = defaultdict(set)

    for location in data:
        for date in data[location]:
            for person in data[location][date]:
                for image_name, label, image_index in data[location][date][person]:
                    images_per_person[person].add(image_name)

    # Find intersection of all people's images
    if not images_per_person:
        return set()

    common_images = set(images_per_person[FILTER_NAMES[0]])
    for person in FILTER_NAMES[1:]:
        if person in images_per_person:
            common_images &= images_per_person[person]

    return common_images


def analyze_totals_and_pie_charts(data: Dict, common_images: Set[str]):
    """
    Analyze totals and create pie charts.
    """
    print('\n=== TOTALS AND PIE CHARTS ANALYSIS ===')

    # Count total number of labels
    total_labels = 0
    all_labels = []
    labels_per_person = defaultdict(list)

    for location in data:
        for date in data[location]:
            for person in data[location][date]:
                for image_name, label, image_index in data[location][date][person]:
                    # Only count labels for images that all people labeled
                    if image_name in common_images:
                        total_labels += 1
                        all_labels.append(label)
                        labels_per_person[person].append(label)

    print(f'Total number of labels (for common images): {total_labels}')
    print(f'Total number of pictures labeled by ALL people: {len(common_images)}')

    # Count labels
    label_counts = Counter(all_labels)
    print(f'Label distribution: {dict(label_counts)}')

    # Create global color mapping for all possible labels (sorted alphabetically)
    all_unique_labels = sorted(set(all_labels))

    n_labels = len(all_unique_labels)
    if n_labels > 1:
        # Use colormap to generate colors
        cmap = plt.get_cmap('RdYlGn_r')
        colors_gradient = [mcolors.to_hex(cmap(i / (n_labels - 1))) for i in range(n_labels)]
    else:
        # Single color if only one label
        colors_gradient = [mcolors.to_hex(plt.get_cmap('brg')(0.0))]

    # Create consistent color mapping for all labels
    label_color_map = {}
    for i, label in enumerate(all_unique_labels):
        label_color_map[label] = colors_gradient[i]

    # Create pie chart of all labels with sorted labels and consistent colors
    sorted_labels = sorted(label_counts.keys())
    sorted_values = [label_counts[label] for label in sorted_labels]
    colors = [label_color_map[label] for label in sorted_labels]

    plt.figure(figsize=(10, 8))
    plt.pie(sorted_values, labels=sorted_labels, autopct='%1.1f%%', colors=colors)
    plt.title('Distribution of All Labels')
    plt.savefig(OUTPUT_DIR / 'all_labels_pie_chart.png', dpi=300, bbox_inches='tight')
    plt.close()

    # Create combined pie chart with all people in one figure
    create_combined_pie_charts(labels_per_person, label_color_map)


def create_combined_pie_charts(labels_per_person: dict, label_color_map: dict):
    """
    Create a combined pie chart with all people in one PNG with automatic grid layout.
    """
    people = sorted(labels_per_person.keys())
    n_people = len(people)

    # Calculate optimal grid dimensions, use a square-ish grid
    cols = int(np.ceil(np.sqrt(n_people)))
    rows = int(np.ceil(n_people / cols))

    # Create figure with subplots
    _, axes = plt.subplots(rows, cols, figsize=(cols * 6, rows * 5))

    # Handle single subplot case
    if n_people == 1:
        axes = [axes]
    elif rows == 1 or cols == 1:
        axes = axes.flatten()
    else:
        axes = axes.flatten()

    # Create pie chart for each person
    for i, person in enumerate(people):
        person_label_counts = Counter(labels_per_person[person])

        # Sort labels alphabetically
        sorted_person_labels = sorted(person_label_counts.keys())
        sorted_person_values = [person_label_counts[label] for label in sorted_person_labels]

        # Use consistent color mapping
        colors = [label_color_map[label] for label in sorted_person_labels]

        # Create pie chart on subplot
        axes[i].pie(sorted_person_values, labels=sorted_person_labels, autopct='%1.1f%%', colors=colors)
        axes[i].set_title(f'{person.title()}', fontsize=14, fontweight='bold')

    # Hide unused subplots
    for i in range(n_people, len(axes)):
        axes[i].set_visible(False)

    plt.suptitle('Label Distribution by Person', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig(OUTPUT_DIR / 'per_person_pie_charts.png', dpi=300, bbox_inches='tight')
    plt.close()

    print(f'Created combined pie chart with {n_people} people in {rows}x{cols} grid')


def analyze_coherence(data: Dict, common_images: Set[str]):
    """
    Analyze coherence between labelers.
    """
    print('\n=== COHERENCE ANALYSIS ===')

    # For each common image, collect all labels from all people
    image_labels = defaultdict(dict)  # {image_name: {person: label}}

    for location in data:
        for date in data[location]:
            for person in data[location][date]:
                for image_name, label, image_index in data[location][date][person]:
                    if image_name in common_images:
                        image_labels[image_name][person] = label

    # Group images by coherence level
    coherence_groups = defaultdict(list)  # {num_agreeing: [(image, winning_label, other_labels)]}

    for image_name, person_labels in image_labels.items():
        if len(person_labels) != len(FILTER_NAMES):
            continue  # Skip if not all people labeled this image

        label_counts = Counter(person_labels.values())
        most_common_label, most_common_count = label_counts.most_common(1)[0]

        # Find other labels and who gave them
        other_labels = []
        for person, label in person_labels.items():
            if label != most_common_label:
                other_labels.append(f'{person}:{label}')

        coherence_groups[most_common_count].append((image_name, most_common_label, other_labels))

    # Export coherence files
    for agreement_count, images_data in coherence_groups.items():
        filename = f'coherence_{agreement_count}_people_agree.csv'
        with open(OUTPUT_DIR / filename, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['image_name', 'winning_label', 'other_labels'])
            for image_name, winning_label, other_labels in images_data:
                writer.writerow([image_name, winning_label, ';'.join(other_labels)])

        print(f'Exported {len(images_data)} images with {agreement_count} people agreeing to {filename}')

    return coherence_groups


def create_agreement_bar_chart(coherence_groups: dict):
    """
    Create a bar chart showing the amount of images for each agreement level.
    """
    print('\n=== AGREEMENT LEVELS BAR CHART ===')

    # Sort agreement levels in descending order
    agreement_levels = sorted(coherence_groups.keys(), reverse=True)
    image_counts = [len(coherence_groups[level]) for level in agreement_levels]
    total_images = sum(image_counts)

    # Create bar chart with explicit x-positions to ensure descending order
    plt.figure(figsize=(10, 6))
    x_positions = range(len(agreement_levels))  # 0, 1, 2, 3...
    plt.bar(x_positions, image_counts, color='skyblue', edgecolor='navy', alpha=0.7)

    # Add value labels on top of bars
    for i, count in enumerate(image_counts):
        percentage = (count / total_images) * 100 if total_images > 0 else 0
        label = f'{count} ({percentage:.1f}%)'
        plt.text(
            i,
            count + 5,
            label,
            ha='center',
            va='bottom',
            fontweight='bold',
        )

    plt.xlabel('Number of People in Agreement')
    plt.ylabel('Number of Images')
    plt.title(f'Distribution of Images by Agreement Level (Total: {total_images})')
    plt.grid(axis='y', alpha=0.3)

    # Set x-axis labels to show agreement levels in descending order
    plt.xticks(x_positions, agreement_levels)

    plt.tight_layout()
    plt.savefig(OUTPUT_DIR / 'agreement_levels_bar_chart.png', dpi=300, bbox_inches='tight')
    plt.close()

    # Print summary
    print('Agreement level distribution:')
    for level, count in zip(agreement_levels, image_counts):
        percentage = (count / total_images) * 100 if total_images > 0 else 0
        print(f'  {level} people agree: {count} images ({percentage:.1f}%)')

    print(f'Total images analyzed: {total_images}')


def analyze_outliers(data: Dict, common_images: Set[str]):
    """
    Analyze how often each person was the outlier when N-1 people agreed.
    """
    print('\n=== OUTLIER ANALYSIS ===')

    # Collect labels for each person for common images
    image_labels = defaultdict(dict)  # {image_name: {person: label}}

    for location in data:
        for date in data[location]:
            for person in data[location][date]:
                for image_name, label, image_index in data[location][date][person]:
                    if image_name in common_images:
                        image_labels[image_name][person] = label

    # Count outliers for each person
    outlier_counts = defaultdict(int)
    total_outlier_cases = 0

    for image_name, person_labels in image_labels.items():
        if len(person_labels) != len(FILTER_NAMES):
            continue  # Skip if not all people labeled this image

        label_counts = Counter(person_labels.values())

        # Check if there's a case where N-1 people agree (majority but not unanimous)
        if len(label_counts) == 2:  # Only two different labels given
            most_common_label, most_common_count = label_counts.most_common(1)[0]

            # If N-1 people agree (one person is the outlier)
            if most_common_count == len(FILTER_NAMES) - 1:
                total_outlier_cases += 1

                # Find who was the outlier
                for person, label in person_labels.items():
                    if label != most_common_label:
                        outlier_counts[person] += 1
                        break

    # Create bar chart
    people = sorted(FILTER_NAMES)
    outlier_values = [outlier_counts[person] for person in people]

    plt.figure(figsize=(10, 6))
    bars = plt.bar(
        [p.title() for p in people],
        outlier_values,
        color='lightcoral',
        edgecolor='darkred',
        alpha=0.7,
    )

    # Add value labels on top of bars
    for bar, count in zip(bars, outlier_values):
        plt.text(
            bar.get_x() + bar.get_width() / 2,
            bar.get_height() + 0.5,
            str(count),
            ha='center',
            va='bottom',
            fontweight='bold',
        )

    plt.xlabel('Person')
    plt.ylabel('Number of Times as Outlier')
    plt.title(
        f'Outlier Analysis: How Often Each Person Disagreed When Others Agreed (Total cases: {total_outlier_cases})'
    )
    plt.grid(axis='y', alpha=0.3)
    plt.xticks(rotation=45)

    plt.tight_layout()
    plt.savefig(OUTPUT_DIR / 'outlier_analysis_bar_chart.png', dpi=300, bbox_inches='tight')
    plt.close()

    # Print summary
    print('Outlier analysis results:')
    print(f'Total cases where N-1 people agreed: {total_outlier_cases}')
    for person in people:
        count = outlier_counts[person]
        percentage = (count / total_outlier_cases) * 100 if total_outlier_cases > 0 else 0
        print(f'  {person}: {count} times outlier ({percentage:.1f}%)')


def parse_image_metadata(image_name: str) -> Tuple[str, str, str, str]:
    """
    Parse image metadata from filename.
    Returns: (date, time, size, flight)

    Expected format: YYYY-MM-DD_HH-MM-SS.mmm_sizeXX_flightN.png
    """
    # Extract date (YYYY-MM-DD)
    date_match = re.match(r'(\d{4}-\d{2}-\d{2})', image_name)
    date = date_match.group(1) if date_match else ''

    # Extract time (HH-MM-SS.mmm)
    time_match = re.search(r'_(\d{2}-\d{2}-\d{2}\.\d{3})_', image_name)
    time = time_match.group(1) if time_match else ''

    # Extract size
    size_match = re.search(r'_size([^_]+)_', image_name)
    size = size_match.group(1) if size_match else ''

    # Extract flight
    flight_match = re.search(r'_flight(\d+)\.', image_name)
    flight = flight_match.group(1) if flight_match else ''

    return date, time, size, flight


def export_csv_per_image(data: Dict, common_images: Set[str]):
    """
    Export CSV with one row per image, ordered by date, location, and time.
    Only includes images that are labeled by all people (common images).
    Columns: location, date, number, size, flight, labels (one column per person)
    """
    print('\n=== EXPORTING CSV PER IMAGE ===')

    # Collect all image data
    image_data = []  # List of dicts with image info
    all_people = set()

    for location in data:
        for date in data[location]:
            # Collect all images for this location/date combination
            images_for_date = defaultdict(dict)  # {image_name: {person: label}}

            for person in data[location][date]:
                all_people.add(person)
                for image_name, label, image_index in data[location][date][person]:
                    # Only include images that are in common_images (labeled by all people)
                    if label and image_name in common_images:
                        images_for_date[image_name][person] = (label, image_index)

            # Process each image for this date/location
            for image_name, person_data in images_for_date.items():
                date_parsed, time, size, flight = parse_image_metadata(image_name)

                # Get image_index from any person (should be consistent across all people)
                image_index = None
                person_labels = {}
                for person, (label, idx) in person_data.items():
                    person_labels[person] = label
                    if image_index is None:
                        image_index = idx

                image_info = {
                    'location': location,
                    'date': date_parsed or date,  # Use parsed date or fallback to directory date
                    'time': time,
                    'index': image_index,
                    'size': size,
                    'flight': flight,
                    'image_name': image_name,
                    'labels': person_labels,
                }
                image_data.append(image_info)

    # Sort by date, then location, then time
    image_data.sort(key=lambda x: (x['date'], x['location'], x['time']))

    # Create CSV
    all_people_sorted = sorted(all_people)
    header = ['location', 'date', 'time', 'index', 'size', 'flight'] + all_people_sorted

    csv_filename = OUTPUT_DIR / 'images_export.csv'
    with open(csv_filename, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(header)

        for image_info in image_data:
            row = [
                image_info['location'],
                image_info['date'],
                image_info['time'],
                image_info['index'],
                image_info['size'],
                image_info['flight'],
            ]

            # Add label columns for each person
            for person in all_people_sorted:
                label = image_info['labels'].get(person, '')
                row.append(label)

            writer.writerow(row)

    print(f'Exported {len(image_data)} images to {csv_filename}')
    print(f'Columns: {", ".join(header)}')
    print(f'People included: {", ".join(all_people_sorted)}')


def analyze_coherence_matrix(data: Dict, common_images: Set[str]):
    """
    Create a coherence matrix showing correlation between every person.
    """
    print('\n=== COHERENCE MATRIX ANALYSIS ===')

    # Collect labels for each person for common images
    person_labels = defaultdict(dict)  # {person: {image: label}}

    for location in data:
        for date in data[location]:
            for person in data[location][date]:
                for image_name, label, image_index in data[location][date][person]:
                    if image_name in common_images:
                        person_labels[person][image_name] = label

    # Calculate correlation matrix
    people = sorted(FILTER_NAMES)
    correlation_matrix = np.zeros((len(people), len(people)))
    coherence_scores = {}

    for i, person1 in enumerate(people):
        agreements_with_others = []

        for j, person2 in enumerate(people):
            if i == j:
                correlation_matrix[i][j] = 1.0  # Perfect correlation with self
                continue

            # Find common images between these two people
            common_images_pair = set(person_labels[person1].keys()) & set(person_labels[person2].keys())

            if not common_images_pair:
                correlation_matrix[i][j] = 0.0
                continue

            # Calculate agreement percentage
            agreements = 0
            for image in common_images_pair:
                if person_labels[person1][image] == person_labels[person2][image]:
                    agreements += 1

            agreement_rate = agreements / len(common_images_pair)
            correlation_matrix[i][j] = agreement_rate
            agreements_with_others.append(agreement_rate)

        # Calculate coherence score (average agreement with all others)
        if agreements_with_others:
            coherence_scores[person1] = np.mean(agreements_with_others)
        else:
            coherence_scores[person1] = 0.0

    # Create extended matrix with coherence scores as additional column
    extended_matrix = np.zeros((len(people), len(people) + 1))
    extended_matrix[:, :-1] = correlation_matrix
    for i, person in enumerate(people):
        extended_matrix[i, -1] = coherence_scores[person]

    # Create and save correlation matrix visualization with coherence scores
    plt.figure(figsize=(12, 8))
    im = plt.imshow(extended_matrix, cmap='RdYlGn', vmin=0, vmax=1)
    plt.colorbar(im, label='Agreement Rate / Coherence Score')

    # Add text annotations
    for i in range(len(people)):
        for j in range(len(people)):
            plt.text(
                j,
                i,
                f'{correlation_matrix[i, j]:.2f}',
                ha='center',
                va='center',
                color='black',
                fontweight='bold',
            )
        # Add coherence score annotation
        plt.text(
            len(people),
            i,
            f'{coherence_scores[people[i]]:.2f}',
            ha='center',
            va='center',
            color='black',
            fontweight='bold',
        )

    # Set up labels
    people_title = [p.title() for p in people]
    x_labels = people_title + ['Coherence Score']
    plt.xticks(range(len(people) + 1), x_labels, rotation=45)
    plt.yticks(range(len(people)), people_title)
    plt.title('Coherence Matrix - Agreement Rates Between Labelers with Coherence Scores')
    plt.tight_layout()
    plt.savefig(OUTPUT_DIR / 'coherence_matrix.png', dpi=300, bbox_inches='tight')
    plt.close()

    # Export correlation matrix as CSV
    with open(OUTPUT_DIR / 'coherence_matrix.csv', 'w', newline='') as f:
        writer = csv.writer(f)

        # Header with coherence scores
        header = ['Person'] + people + ['Coherence_Score']
        writer.writerow(header)

        for i, person in enumerate(people):
            row = (
                [person]
                + [f'{correlation_matrix[i][j]:.3f}' for j in range(len(people))]
                + [f'{coherence_scores[person]:.3f}']
            )
            writer.writerow(row)

    # Print results
    print('Coherence Matrix (Agreement Rates):')
    print('Person', end='')
    for person in people:
        print(f'\t{person[:8]}', end='')
    print('\tCoherence')

    for i, person1 in enumerate(people):
        print(f'{person1[:8]}', end='')
        for j, person2 in enumerate(people):
            print(f'\t{correlation_matrix[i][j]:.3f}', end='')
        print(f'\t{coherence_scores[person1]:.3f}')

    print('\nCoherence Scores (average agreement with others):')
    for person in sorted(coherence_scores.keys()):
        print(f'  {person}: {coherence_scores[person]:.3f}')

    return correlation_matrix, coherence_scores


def main():
    """Main analysis function."""
    ensure_output_dir()

    print('Loading label data...')
    data = load_labels_data()

    print(f'Loaded data for locations: {list(data.keys())}')
    for location in data:
        print(f'  {location}: {len(data[location])} dates')
        for date in data[location]:
            print(f'    {date}: {list(data[location][date].keys())}')

    # Find images labeled by all people
    common_images = get_all_images_labeled_by_all_people(data)
    print(f'\nFound {len(common_images)} images labeled by all people')

    # Export common images list
    with open(OUTPUT_DIR / 'common_images.txt', 'w') as f:
        for image in sorted(common_images):
            f.write(f'{image}\n')

    # Export CSV per image
    export_csv_per_image(data, common_images)

    # Run analyses
    analyze_totals_and_pie_charts(data, common_images)
    coherence_groups = analyze_coherence(data, common_images)
    create_agreement_bar_chart(coherence_groups)
    analyze_outliers(data, common_images)
    analyze_coherence_matrix(data, common_images)

    print(f'\nAnalysis complete. Results saved to {OUTPUT_DIR}')


if __name__ == '__main__':
    main()
